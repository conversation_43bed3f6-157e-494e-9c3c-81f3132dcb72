package cmc.pad.resource.admin.api.controller;

import org.junit.Assert;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 控制器基础测试类
 * 提供通用的HTTP请求和响应断言方法
 *
 * <AUTHOR>
 * @Date 2025/09/02
 * @Version 1.0
 */
public abstract class BaseControllerTest {

    protected static final String BASE_URL = "http://localhost:8125";

    /**
     * 发送HTTP GET请求
     *
     * @param path 请求路径
     * @return 响应内容
     * @throws Exception 请求异常
     */
    protected String httpGet(String path) throws Exception {
        URL url = new URL(BASE_URL + path);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(10000);

        int responseCode = conn.getResponseCode();
        BufferedReader reader;

        if (responseCode >= 200 && responseCode < 300) {
            reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        } else {
            reader = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();
        conn.disconnect();

        return response.toString();
    }

    /**
     * 断言响应状态码和消息
     *
     * @param response 响应内容
     * @param status   期望的状态码
     * @param msg      期望的消息
     * @param data     期望的数据（可选）
     */
    protected void assertResponseStatusAndMsg(String response, int status, String msg, String... data) {
        System.out.println(response);
        Assert.assertTrue("status:" + status + " msg: " + msg + ", but response: " + response, 
                response.contains("\"status\":" + status));
        Assert.assertTrue("msg: " + msg + ", but response: " + response, 
                response.contains("\"msg\":\"" + msg + "\""));
        if (data != null && data.length != 0) {
            Assert.assertTrue("data: " + data[0] + ", but response: " + response, 
                    response.contains("\"data\":" + data[0]));
        }
    }

    /**
     * 测试参数cinema_code为空但city_level和cinema_level不为空的情况
     * 子类需要实现具体的API调用
     */
    protected abstract String getApi(String param) throws Exception;
}