# 控制器测试套件使用说明

## 测试套件介绍

本目录包含了两个测试套件，用于批量运行控制器测试：

### 1. LeasingControllerTestSuite
专门用于运行租赁相关的控制器测试：
- AdvertisingPointLeasingControllerBasicTest（宣传点位租赁测试）
- FixedPointLeasingControllerBasicTest（固定点位租赁测试）

### 2. AllControllerTestSuite
用于运行所有控制器测试，包括：
- 所有租赁相关测试
- 折扣控制器测试
- 库存占用控制器测试
- 灯箱价格控制器测试
- 影厅命名租赁控制器测试
- 资源控制器测试

## 使用方法

### 在IDE中运行
1. 右键点击测试套件类（如 `LeasingControllerTestSuite.java`）
2. 选择 "Run 'LeasingControllerTestSuite'"

### 使用Maven命令行运行
```bash
# 运行租赁控制器测试套件
mvn test -Dtest=LeasingControllerTestSuite

# 运行所有控制器测试套件
mvn test -Dtest=AllControllerTestSuite

# 运行单个测试类
mvn test -Dtest=AdvertisingPointLeasingControllerBasicTest
```

### 使用Gradle命令行运行（如果项目使用Gradle）
```bash
# 运行租赁控制器测试套件
./gradlew test --tests LeasingControllerTestSuite

# 运行所有控制器测试套件
./gradlew test --tests AllControllerTestSuite
```

## 注意事项

1. **服务启动**：运行测试前请确保服务已启动在 `http://localhost:8125`
2. **数据准备**：确保测试数据已正确配置
3. **网络连接**：确保网络连接正常，测试会发送HTTP请求

## 基础测试类

所有控制器测试都继承自 `BaseControllerTest`，该基类提供了：
- 统一的HTTP请求方法
- 统一的响应断言方法
- 可配置的基础URL和超时设置

如需添加新的控制器测试，建议继承 `BaseControllerTest` 并在相应的测试套件中添加新的测试类。