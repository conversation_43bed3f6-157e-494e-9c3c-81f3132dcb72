package cmc.pad.resource.admin.api.controller;

import org.junit.Test;

/**
 * 宣传点位租赁价格控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/01
 * @Version 1.0
 */
public class AdvertisingPointLeasingControllerBasicTest extends BaseControllerTest {

    @Test
    public void testParamCinemaInnerCodeIsNullButCityLevelAndCinemaLevelIsNotNull() throws Exception {
        assertResponseStatusAndMsg(getApi(""), 2006, "影城级别不存在");
        assertResponseStatusAndMsg(getApi("city_level=1"), 101, "city_level城市级别错误");
        assertResponseStatusAndMsg(getApi("city_level=L1"), 2006, "影城级别不存在");
        assertResponseStatusAndMsg(getApi("cinema_level=a"), 2006, "影城级别不存在");
    }

    @Test
    public void testParamCinemaInnerCodeIsNotNull() throws Exception {
        assertResponseStatusAndMsg(getApi("cinema_code=849"), 0, "OK");
        assertResponseStatusAndMsg(getApi("cinema_code=304"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":2.35,\"base_area\":1,\"extended_price\":2.33}]");
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/advertising-point-leasing/price/query?" + param;
        System.out.println(path);
        return httpGet(path);
    }
}