package cmc.pad.resource.admin.api.controller;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * 所有控制器测试套件
 * 用于批量运行所有控制器测试
 *
 * <AUTHOR>
 * @Date 2025/09/02
 * @Version 1.0
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
    AdvertisingPointLeasingControllerBasicTest.class,
    FixedPointLeasingControllerBasicTest.class,
    DiscountControllerTest.class,
    InventoryOccupationControllerTest.class,
    LightBoxPriceControllerTest.class,
    MovieHallNamingLeasingControllerTest.class,
    ResourceControllerTest.class
})
public class AllControllerTestSuite {
    // 测试套件类，无需实现任何方法
    // 通过注解配置要运行的测试类
}