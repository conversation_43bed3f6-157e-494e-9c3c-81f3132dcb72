package cmc.pad.resource.admin.api.controller;

import com.alibaba.fastjson.JSON;
import cmc.pad.resource.admin.service.dto.FixedPointLeasingDto;
import cmc.pad.resource.admin.service.iface.FixedPointLeasingService;
import cmc.pad.resource.admin.api.model.price.FixedPointLeasingPriceModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 固定点位刊例报价
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:04
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("fixed-point-leasing/price")
public class FixedPointLeasingController {

    private final FixedPointLeasingService fixedPointLeasingService;

    @Autowired
    FixedPointLeasingController(FixedPointLeasingService fixedPointLeasingService) {
        this.fixedPointLeasingService = fixedPointLeasingService;
    }


    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated FixedPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated FixedPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(FixedPointLeasingPriceModel.QueryParams params) {
        log.info(">>>查询固定点位刊例价, {}", params);

        // 构建RPC请求参数
        FixedPointLeasingDto.QueryPricesRequest request = new FixedPointLeasingDto.QueryPricesRequest();
        request.setCinemaCode(params.getCinemaCode());
        request.setCityLevel(params.getCityLevel());
        request.setCinemaLevel(params.getCinemaLevel());

        // 调用RPC服务
        FixedPointLeasingDto.QueryPricesResponse response = fixedPointLeasingService.queryPrices(request);

        // 转换响应结果
        List<FixedPointLeasingPriceModel.Info> result;
        if (response.getPriceInfos() != null && !response.getPriceInfos().isEmpty()) {
            result = response.getPriceInfos().stream().map(o -> {
                FixedPointLeasingPriceModel.Info info = new FixedPointLeasingPriceModel.Info();
                info.setCityLevel(o.getCityLevel());
                info.setCinemaLevel(o.getCinemaLevel());
                info.setBasePrice(o.getBasePrice());
                return info;
            }).collect(Collectors.toList());
        } else {
            result = Collections.EMPTY_LIST;
        }
        // 打印响应日志
        log.info(">>>响应:查询固定点位租赁价格查询, request:{} result:{}", request, JSON.toJSONString(result));
        return result;
    }

}
