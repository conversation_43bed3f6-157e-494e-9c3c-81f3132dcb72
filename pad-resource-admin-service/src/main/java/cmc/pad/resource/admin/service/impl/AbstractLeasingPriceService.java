package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.application.AppError;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * 租赁价格服务抽象基类
 * 提炼通用功能，减少代码重复
 *
 * <AUTHOR>
 * @Date 2025/09/02
 * @Version 1.0
 */
@Slf4j
public abstract class AbstractLeasingPriceService {

    protected final CinemaLevelQueryService cinemaLevelQueryService;
    protected final MysqlCinemaRepository cinemaRepository;
    protected final DictionaryDomainService dictionaryDomainService;

    @Autowired
    public AbstractLeasingPriceService(CinemaLevelQueryService cinemaLevelQueryService,
                                     MysqlCinemaRepository cinemaRepository,
                                     DictionaryDomainService dictionaryDomainService) {
        this.cinemaLevelQueryService = cinemaLevelQueryService;
        this.cinemaRepository = cinemaRepository;
        this.dictionaryDomainService = dictionaryDomainService;
    }

    /**
     * 处理查询参数，获取城市级别和影城级别
     *
     * @param cinemaCode 影城编码
     * @param cityLevel 城市级别
     * @param cinemaLevel 影城级别
     * @return 处理后的级别信息 [cityLevel, cinemaLevel]
     */
    protected String[] processQueryParams(String cinemaCode, String cityLevel, String cinemaLevel) {
        String cityDistrictLevel = cityLevel;
        String processedCinemaLevel = cinemaLevel;

        if (StringUtils.isNotBlank(cinemaCode)) {
            CinemaLevelInfo cinemaLevelInfo = getCinemaLevel(cinemaCode);
            processedCinemaLevel = cinemaLevelInfo.getCinemaLevel();
            cityDistrictLevel = cinemaLevelInfo.getCityDistrictLevel();
        } else {
            validateCityDistrictLevel(cityDistrictLevel);
            validateCinemaLevel(processedCinemaLevel);
        }

        return new String[]{cityDistrictLevel, processedCinemaLevel};
    }

    /**
     * 分转元
     *
     * @param cent 分
     * @return 元
     */
    protected float centConvertYuan(Integer cent) {
        if (cent == null) {
            return 0f;
        }
        return cent / 100.0f;
    }

    /**
     * 获取影城级别信息
     *
     * @param cinemaCode 影城编码
     * @return 影城级别信息
     */
    protected CinemaLevelInfo getCinemaLevel(String cinemaCode) {
        CinemaLevelInfo cinema = cinemaLevelQueryService.getCinema(cinemaCode);
        log.info(">>>影城内码:{}, 影城信息:{}", cinemaCode, cinema);
        if (Objects.isNull(cinema)) {
            throw AppError.CINEMA_LEVEL_NOT_CONFIG.toException();
        }
        String cinemaLevel = cinema.getCinemaLevel();
        if (StringUtils.isBlank(cinemaLevel)) {
            throw AppError.CINEMA_LEVEL_NOT_CONFIG.toException();
        }
        String cityDistrictLevel = cinema.getCityDistrictLevel();
        if (StringUtils.isBlank(cityDistrictLevel)) {
            throw AppError.CITY_DISTRICT_NO_LEVEL.toException();
        }
        return cinema;
    }

    /**
     * 验证影城级别
     *
     * @param cinemaLevel 影城级别
     */
    protected void validateCinemaLevel(String cinemaLevel) {
        List<String> levelCodes = dictionaryDomainService.allCinemaLevelCodes();
        if (!levelCodes.contains(cinemaLevel)) {
            throw AppError.CINEMA_LEVEL_NOT_EXIST.toException();
        }
    }

    /**
     * 验证城市级别
     *
     * @param cityLevel 城市级别
     */
    protected void validateCityDistrictLevel(String cityLevel) {
        List<String> codes = dictionaryDomainService.allCityLevelCodes();
        if (!codes.contains(cityLevel)) {
            throw AppError.CINEMA_LEVEL_NOT_EXIST.toException();
        }
    }

    /**
     * 记录查询日志
     *
     * @param serviceName 服务名称
     * @param request 请求参数
     */
    protected void logQueryStart(String serviceName, Object request) {
        log.info(">>>查询{}刊例价, {}", serviceName, request);
    }

    /**
     * 记录查询结果日志
     *
     * @param serviceName 服务名称
     * @param request 请求参数
     * @param response 响应结果
     */
    protected void logQueryResult(String serviceName, Object request, Object response) {
        log.info(">>>查询{}刊例价, request:{} result:{}", serviceName, request, response);
    }
}
