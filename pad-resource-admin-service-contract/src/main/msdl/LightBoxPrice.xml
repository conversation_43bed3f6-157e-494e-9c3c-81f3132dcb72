<?xml version="1.0" encoding="utf-8" ?>
<root javaPackageBase="cmc.pad.resource.admin.service">
    <services description="灯箱价格服务">
        <service name="LightBoxPrice" description="灯箱价格服务">
            <method name="QueryPrices" description="查询灯箱价格">
                <request description="查询灯箱价格请求参数">
                    <field name="cityLevel" type="string" description="城市级别编码" order="1"/>
                    <field name="cinemaLevel" type="string" description="影院级别编码" order="2"/>
                    <field name="cinemaCode" type="string"
                           description="影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效" order="3"/>
                </request>
                <response description="查询灯箱价格响应结果">
                    <field modifier="repeated" name="priceInfos" type="PriceInfo" description="价格信息列表" order="1"/>
                </response>
            </method>
        </service>
    </services>
    <dataTypes>
        <dataType name="PriceInfo" description="灯箱价格信息">
            <field name="cityLevel" type="string" description="城市级别编码" order="1"/>
            <field name="cinemaLevel" type="string" description="影院级别编码" order="2"/>
            <field name="basePrice" type="float" description="基础价，单位：元/月" order="3"/>
            <field name="baseArea" type="int32" description="基础面积，单位：平米" order="4"/>
            <field name="extendedPrice" type="float" description="续价，单位：元/平米/月" order="5"/>
        </dataType>
    </dataTypes>
</root>
